# Joy Trip - Tourist Attractions Map Application

A modern, responsive web application that helps users discover tourist attractions near their location using Google Maps APIs. The application provides real-time location-based search, detailed attraction information, and turn-by-turn navigation.

## Features

### 🗺️ **Interactive Google Maps Integration**
- High-quality Google Maps with multiple view types (roadmap, satellite, hybrid, terrain)
- Smooth pan and zoom controls
- Custom markers for user location and attractions

### 📍 **Advanced Location Services**
- Automatic geolocation detection with enhanced error handling
- Manual location search with Google Geocoding API
- Fallback options when geolocation fails

### 🎯 **Smart Attraction Discovery**
- Real-time search using Google Places API
- Multiple attraction categories: museums, parks, amusement parks, zoos, aquariums, art galleries, religious sites, and more
- Configurable search radius (25km to 150km)
- Distance calculation and sorting

### 📱 **Detailed Place Information**
- High-resolution photos from Google Places
- User ratings and reviews
- Opening hours and contact information
- Price level indicators
- Direct links to official websites

### 🧭 **Navigation & Directions**
- Multiple route options: driving, walking, public transit
- Real-time distance and duration estimates
- Integration with Google Maps mobile app
- Turn-by-turn navigation support

### 📱 **Responsive Design**
- Mobile-first responsive layout
- Touch-friendly interface
- Optimized for all screen sizes

## Setup Instructions

### Prerequisites
- A modern web browser with JavaScript enabled
- Google Cloud Platform account
- Basic knowledge of HTML/CSS/JavaScript

### 1. Google Cloud Platform Setup

1. **Create a Google Cloud Project**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select an existing one
   - Note your project ID

2. **Enable Required APIs**
   Navigate to "APIs & Services" > "Library" and enable:
   - **Maps JavaScript API** - For interactive map display
   - **Places API** - For finding tourist attractions
   - **Directions API** - For navigation and route planning
   - **Geocoding API** - For address search functionality

3. **Create API Credentials**
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "API Key"
   - Copy your API key

4. **Secure Your API Key (Recommended)**
   - Click on your API key to edit it
   - Under "Application restrictions", select "HTTP referrers"
   - Add your domain(s), e.g., `https://yourdomain.com/*`
   - Under "API restrictions", select "Restrict key" and choose the APIs you enabled

### 2. Application Configuration

1. **Configure API Key**
   Open `config.js` and replace the placeholder:
   ```javascript
   GOOGLE_MAPS_API_KEY: 'YOUR_ACTUAL_API_KEY_HERE'
   ```

2. **Optional: Customize Settings**
   You can modify these settings in `config.js`:
   ```javascript
   DEFAULT_LOCATION: { lat: 40.7128, lng: -74.0060 }, // Default map center
   DEFAULT_ZOOM: 12,                                   // Initial zoom level
   SEARCH_RADIUS: 100000,                             // Default search radius (meters)
   MAX_RESULTS: 20                                    // Maximum attractions to display
   ```

### 3. Deployment

#### Option A: Local Development
1. Clone or download the project files
2. Open `index.html` in a web browser
3. Allow location access when prompted

#### Option B: Web Server Deployment
1. Upload all files to your web server
2. Ensure HTTPS is enabled (required for geolocation)
3. Update API key restrictions to include your domain

## File Structure

```
joy-trip/
├── index.html          # Main HTML structure
├── styles.css          # Complete CSS styling
├── script.js           # Core JavaScript functionality
├── config.js           # Configuration and API setup
├── .env               # Environment variables (template)
└── README.md          # This documentation
```

## Usage Guide

### Getting Started
1. **Open the Application**
   - Load the webpage in your browser
   - The map will initialize with a default location

2. **Set Your Location**
   - Click "Get My Location" to use automatic geolocation
   - Or use the manual search if geolocation fails
   - Allow location access when prompted by your browser

3. **Discover Attractions**
   - Attractions will automatically load within your selected radius
   - Use the category filter to narrow results
   - Adjust the search radius as needed

### Exploring Attractions
- **Map View**: Click markers on the map for quick information
- **List View**: Browse attractions in the sidebar
- **Detailed View**: Click any attraction for comprehensive details

### Getting Directions
1. Click on any attraction to open details
2. Click "Get Directions"
3. Choose your preferred travel mode:
   - 🚗 Driving
   - 🚶 Walking  
   - 🚌 Public Transit
4. View route information and optionally open in Google Maps app

## API Usage & Limits

### Google Maps APIs
- **Maps JavaScript API**: Used for map display and interaction
- **Places API**: Limited to 1000 requests per day (free tier)
- **Directions API**: Limited to 2500 requests per day (free tier)
- **Geocoding API**: Limited to 40,000 requests per month (free tier)

### Rate Limiting
The application includes built-in error handling for API rate limits and will display user-friendly messages when limits are exceeded.

## Troubleshooting

### Common Issues

**"Google Maps API key is not configured"**
- Ensure you've replaced the placeholder API key in `config.js`
- Verify the API key is correct and active

**"Location access denied"**
- Enable location services in your browser
- Use the manual location search as an alternative
- Ensure the site is served over HTTPS

**"No attractions found"**
- Increase the search radius
- Try a different location
- Check if Places API is enabled and has quota remaining

**Map not loading**
- Check browser console for error messages
- Verify all required APIs are enabled
- Ensure API key has proper restrictions set

### Browser Compatibility
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve the application.

## License

This project is open source and available under the [MIT License](LICENSE).

## Support

For technical support or questions:
1. Check the troubleshooting section above
2. Review Google Maps API documentation
3. Submit an issue with detailed error information

---

**Note**: This application requires active Google Maps API keys with sufficient quota. Monitor your API usage through the Google Cloud Console to avoid unexpected charges.
