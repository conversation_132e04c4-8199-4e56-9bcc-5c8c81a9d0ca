/**
 * <PERSON> Trip - Tourist Attractions Map Application
 * Enhanced with Google Maps API integration
 */

// Global variables
let map;
let userLocation = null;
let attractions = [];
let markers = [];
let userMarker = null;
let placesService;
let directionsService;
let directionsRenderer;
let geocoder;
let currentInfoWindow = null;
let selectedPlace = null;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeGoogleMapsAPI()
        .then(() => {
            initializeMap();
            setupEventListeners();
            hideMapLoading();
        })
        .catch(error => {
            console.error('Failed to initialize Google Maps:', error);
            hideMapLoading();
        });
});

/**
 * Initialize Google Maps
 */
function initializeMap() {
    try {
        // Create map centered on default location
        const defaultLocation = getConfig('DEFAULT_LOCATION');

        map = new google.maps.Map(document.getElementById('map'), {
            center: defaultLocation,
            zoom: getConfig('DEFAULT_ZOOM'),
            minZoom: getConfig('MIN_ZOOM'),
            maxZoom: getConfig('MAX_ZOOM'),
            mapTypeControl: true,
            mapTypeControlOptions: {
                style: google.maps.MapTypeControlStyle.HORIZONTAL_BAR,
                position: google.maps.ControlPosition.TOP_CENTER,
            },
            zoomControl: true,
            zoomControlOptions: {
                position: google.maps.ControlPosition.RIGHT_CENTER,
            },
            scaleControl: true,
            streetViewControl: true,
            streetViewControlOptions: {
                position: google.maps.ControlPosition.RIGHT_TOP,
            },
            fullscreenControl: true,
        });

        // Initialize services
        placesService = new google.maps.places.PlacesService(map);
        directionsService = new google.maps.DirectionsService();
        directionsRenderer = new google.maps.DirectionsRenderer({
            draggable: true,
            panel: null
        });
        geocoder = new google.maps.Geocoder();

        console.log('Google Maps initialized successfully');
    } catch (error) {
        console.error('Error initializing Google Maps:', error);
        showError('Failed to initialize map. Please refresh the page and try again.');
    }
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    // Location button
    document.getElementById('locationBtn').addEventListener('click', getUserLocation);

    // Manual location search
    document.getElementById('searchLocationBtn').addEventListener('click', searchManualLocation);
    document.getElementById('locationInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchManualLocation();
        }
    });

    // Filter controls
    document.getElementById('radiusSelect').addEventListener('change', filterAttractions);
    document.getElementById('categoryFilter').addEventListener('change', filterAttractions);

    // Modal controls
    document.getElementById('modalClose').addEventListener('click', closeModal);
    document.getElementById('attractionModal').addEventListener('click', function(e) {
        if (e.target === this) closeModal();
    });

    // Directions button
    document.getElementById('getDirectionsBtn').addEventListener('click', function() {
        const directionsOptions = document.getElementById('directionsOptions');
        directionsOptions.style.display = directionsOptions.style.display === 'none' ? 'flex' : 'none';
    });
}

/**
 * Get user's current location using Geolocation API
 */
function getUserLocation() {
    const locationBtn = document.getElementById('locationBtn');
    const locationStatus = document.getElementById('locationStatus');

    // Update button state
    locationBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Getting Location...';
    locationBtn.disabled = true;

    // Hide manual location input
    document.getElementById('manualLocation').style.display = 'none';

    if (!navigator.geolocation) {
        showLocationError(getConfig('ERROR_MESSAGES').GEOLOCATION_DENIED, true);
        return;
    }

    // Show loading status
    locationStatus.className = 'location-status';
    locationStatus.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Getting your location...';

    navigator.geolocation.getCurrentPosition(
        function(position) {
            userLocation = {
                lat: position.coords.latitude,
                lng: position.coords.longitude
            };

            // Update UI
            locationStatus.className = 'location-status success';
            locationStatus.innerHTML = '<i class="fas fa-check-circle"></i> Location found! Searching for nearby attractions...';

            // Reset button
            locationBtn.innerHTML = '<i class="fas fa-location-arrow"></i> Update Location';
            locationBtn.disabled = false;

            // Update map and find attractions
            updateMapWithUserLocation();
            findNearbyAttractions();
        },
        function(error) {
            let errorMessage = getConfig('ERROR_MESSAGES').GEOLOCATION_DENIED;
            let showManualInput = true;

            switch(error.code) {
                case error.PERMISSION_DENIED:
                    errorMessage = getConfig('ERROR_MESSAGES').GEOLOCATION_DENIED;
                    break;
                case error.POSITION_UNAVAILABLE:
                    errorMessage = getConfig('ERROR_MESSAGES').GEOLOCATION_UNAVAILABLE;
                    break;
                case error.TIMEOUT:
                    errorMessage = getConfig('ERROR_MESSAGES').GEOLOCATION_TIMEOUT;
                    break;
            }
            showLocationError(errorMessage, showManualInput);
        },
        {
            enableHighAccuracy: true,
            timeout: 15000,
            maximumAge: 300000 // 5 minutes
        }
    );
}

/**
 * Search for location manually using Geocoding API
 */
function searchManualLocation() {
    const locationInput = document.getElementById('locationInput');
    const searchBtn = document.getElementById('searchLocationBtn');
    const locationStatus = document.getElementById('locationStatus');

    const query = locationInput.value.trim();
    if (!query) {
        showError('Please enter a location to search.');
        return;
    }

    // Update button state
    searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Searching...';
    searchBtn.disabled = true;

    // Show loading status
    locationStatus.className = 'location-status';
    locationStatus.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Searching for location...';

    geocoder.geocode({ address: query }, function(results, status) {
        // Reset button
        searchBtn.innerHTML = '<i class="fas fa-search"></i> Search';
        searchBtn.disabled = false;

        if (status === 'OK' && results[0]) {
            const location = results[0].geometry.location;
            userLocation = {
                lat: location.lat(),
                lng: location.lng()
            };

            // Update UI
            locationStatus.className = 'location-status success';
            locationStatus.innerHTML = `<i class="fas fa-check-circle"></i> Found: ${results[0].formatted_address}`;

            // Hide manual input
            document.getElementById('manualLocation').style.display = 'none';

            // Update map and find attractions
            updateMapWithUserLocation();
            findNearbyAttractions();
        } else {
            showError('Location not found. Please try a different search term.');
        }
    });
}

/**
 * Show location error with optional manual input
 */
function showLocationError(message, showManualInput = false) {
    const locationBtn = document.getElementById('locationBtn');
    const locationStatus = document.getElementById('locationStatus');
    const manualLocation = document.getElementById('manualLocation');

    locationStatus.className = 'location-status error';
    locationStatus.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;

    locationBtn.innerHTML = '<i class="fas fa-location-arrow"></i> Try Again';
    locationBtn.disabled = false;

    if (showManualInput) {
        manualLocation.style.display = 'block';
    }
}

/**
 * Update map with user location using Google Maps
 */
function updateMapWithUserLocation() {
    if (!userLocation) return;

    try {
        // Remove existing user marker
        if (userMarker) {
            userMarker.setMap(null);
        }

        // Create user location marker
        userMarker = new google.maps.Marker({
            position: userLocation,
            map: map,
            title: 'Your Location',
            icon: {
                path: google.maps.SymbolPath.CIRCLE,
                scale: 10,
                fillColor: '#4285F4',
                fillOpacity: 1,
                strokeColor: '#ffffff',
                strokeWeight: 3,
            },
            animation: google.maps.Animation.DROP
        });

        // Add info window for user location
        const userInfoWindow = new google.maps.InfoWindow({
            content: '<div style="text-align: center;"><strong>Your Location</strong></div>'
        });

        userMarker.addListener('click', function() {
            if (currentInfoWindow) {
                currentInfoWindow.close();
            }
            userInfoWindow.open(map, userMarker);
            currentInfoWindow = userInfoWindow;
        });

        // Center map on user location
        map.setCenter(userLocation);
        map.setZoom(getConfig('DEFAULT_ZOOM'));

        console.log('User location updated on map');
    } catch (error) {
        console.error('Error updating user location on map:', error);
        showError('Failed to update your location on the map.');
    }
}

/**
 * Calculate distance between two points using Google Maps Geometry library
 */
function calculateDistance(lat1, lng1, lat2, lng2) {
    const point1 = new google.maps.LatLng(lat1, lng1);
    const point2 = new google.maps.LatLng(lat2, lng2);
    return google.maps.geometry.spherical.computeDistanceBetween(point1, point2) / 1000; // Convert to kilometers
}

/**
 * Find nearby attractions using Google Places API
 */
function findNearbyAttractions() {
    if (!userLocation || !placesService) return;

    const radius = parseInt(document.getElementById('radiusSelect').value) * 1000; // Convert to meters
    const category = document.getElementById('categoryFilter').value;

    // Show loading state
    const locationStatus = document.getElementById('locationStatus');
    locationStatus.className = 'location-status';
    locationStatus.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Finding nearby attractions...';

    // Clear existing attractions and markers
    clearMarkers();
    attractions = [];

    // Determine place types to search for
    let types = [];
    if (category === 'all') {
        types = getConfig('PLACE_TYPES');
    } else {
        types = [category];
    }

    // Search for each type
    let searchPromises = types.map(type => searchPlacesByType(type, radius));

    Promise.all(searchPromises)
        .then(results => {
            // Flatten and deduplicate results
            const allPlaces = results.flat();
            const uniquePlaces = deduplicatePlaces(allPlaces);

            // Process and sort attractions
            attractions = uniquePlaces.map(place => {
                const distance = calculateDistance(
                    userLocation.lat, userLocation.lng,
                    place.geometry.location.lat(), place.geometry.location.lng()
                );

                return {
                    id: place.place_id,
                    name: place.name,
                    category: place.types[0] || 'tourist_attraction',
                    lat: place.geometry.location.lat(),
                    lng: place.geometry.location.lng(),
                    distance: distance,
                    rating: place.rating || 0,
                    price_level: place.price_level,
                    vicinity: place.vicinity,
                    photos: place.photos || [],
                    place_id: place.place_id,
                    types: place.types || [],
                    opening_hours: place.opening_hours
                };
            }).filter(attraction => attraction.distance <= radius / 1000)
              .sort((a, b) => a.distance - b.distance)
              .slice(0, getConfig('MAX_RESULTS'));

            // Update UI
            locationStatus.className = 'location-status success';
            locationStatus.innerHTML = `<i class="fas fa-check-circle"></i> Found ${attractions.length} attractions nearby`;

            updateAttractionsList();
            updateMapMarkers();
        })
        .catch(error => {
            console.error('Error finding attractions:', error);
            locationStatus.className = 'location-status error';
            locationStatus.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${getConfig('ERROR_MESSAGES').PLACES_API_ERROR}`;
        });
}

/**
 * Search for places by type using Google Places API
 */
function searchPlacesByType(type, radius) {
    return new Promise((resolve, reject) => {
        const request = {
            location: userLocation,
            radius: radius,
            type: type
        };

        placesService.nearbySearch(request, function(results, status) {
            if (status === google.maps.places.PlacesServiceStatus.OK) {
                resolve(results || []);
            } else if (status === google.maps.places.PlacesServiceStatus.ZERO_RESULTS) {
                resolve([]);
            } else {
                console.warn(`Places search failed for type ${type}:`, status);
                resolve([]);
            }
        });
    });
}

/**
 * Remove duplicate places based on place_id
 */
function deduplicatePlaces(places) {
    const seen = new Set();
    return places.filter(place => {
        if (seen.has(place.place_id)) {
            return false;
        }
        seen.add(place.place_id);
        return true;
    });
}

/**
 * Filter attractions based on category and radius
 */
function filterAttractions() {
    if (!userLocation) {
        // If no location is set, trigger a new search
        findNearbyAttractions();
        return;
    }

    if (attractions.length === 0) return;

    const category = document.getElementById('categoryFilter').value;
    const radius = parseInt(document.getElementById('radiusSelect').value);

    let filteredAttractions = attractions.filter(attraction => {
        const withinRadius = attraction.distance <= radius;
        const matchesCategory = category === 'all' ||
                               attraction.category === category ||
                               (attraction.categories && attraction.categories.includes(category));
        return withinRadius && matchesCategory;
    });

    updateAttractionsList(filteredAttractions);
    updateMapMarkers(filteredAttractions);
}

/**
 * Clear all markers from the map
 */
function clearMarkers() {
    markers.forEach(marker => {
        map.removeLayer(marker);
    });
    markers = [];
}

/**
 * Update attractions list in sidebar
 */
function updateAttractionsList(attractionsToShow = attractions) {
    const attractionsList = document.getElementById('attractionsList');
    const attractionCount = document.getElementById('attractionCount');

    attractionCount.textContent = `${attractionsToShow.length} found`;

    if (attractionsToShow.length === 0) {
        attractionsList.innerHTML = `
            <div class="no-attractions">
                <i class="fas fa-search"></i>
                <p>No attractions found in this area.</p>
                <p>Try increasing the search radius or changing the category filter.</p>
            </div>
        `;
        return;
    }

    attractionsList.innerHTML = attractionsToShow.map(attraction => {
        const openingStatus = getOpeningStatus(attraction.opening_hours);
        const stars = generateStarRating(attraction.rating);

        return `
            <div class="attraction-card" onclick="showAttractionDetails('${attraction.id}')" data-id="${attraction.id}">
                <div class="attraction-header">
                    <h4 class="attraction-name">${attraction.name}</h4>
                    <span class="attraction-distance">${attraction.distance.toFixed(1)} km</span>
                </div>
                <div class="attraction-category">${getCategoryLabel(attraction.category)}</div>
                ${attraction.rating > 0 ? `
                    <div class="attraction-rating">
                        <span class="stars">${stars}</span>
                        <span>${attraction.rating.toFixed(1)}</span>
                    </div>
                ` : ''}
                ${openingStatus ? `<div class="attraction-status ${openingStatus.class}">${openingStatus.text}</div>` : ''}
                <p class="attraction-description">${attraction.address || 'Tourist attraction'}</p>
            </div>
        `;
    }).join('');
}

/**
 * Get category label for display
 */
function getCategoryLabel(category) {
    return getConfig('CATEGORY_LABELS')[category] || category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

/**
 * Generate star rating HTML
 */
function generateStarRating(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

    let stars = '★'.repeat(fullStars);
    if (hasHalfStar) stars += '☆';
    stars += '☆'.repeat(emptyStars);

    return stars;
}

/**
 * Get opening status from opening hours
 */
function getOpeningStatus(openingHours) {
    if (!openingHours) return null;

    if (openingHours.open_now === true) {
        return { class: 'open', text: 'Open now' };
    } else if (openingHours.open_now === false) {
        return { class: 'closed', text: 'Closed' };
    }

    return { class: 'unknown', text: 'Hours unknown' };
}

/**
 * Update map markers using Leaflet
 */
function updateMapMarkers(attractionsToShow = attractions) {
    // Clear existing markers
    clearMarkers();

    // Add new markers
    attractionsToShow.forEach((attraction) => {
        const icon = L.divIcon({
            className: 'custom-marker',
            html: '<i class="fas fa-map-marker-alt"></i>',
            iconSize: [30, 30],
            iconAnchor: [15, 30]
        });

        const marker = L.marker([attraction.lat, attraction.lng], { icon })
            .addTo(map)
            .bindPopup(`
                <div style="text-align: center; max-width: 250px;">
                    <h4 style="margin: 0 0 8px 0; color: #333;">${attraction.name}</h4>
                    <p style="margin: 4px 0; color: #666; font-size: 0.9rem;">${getCategoryLabel(attraction.category)}</p>
                    ${attraction.rating > 0 ? `
                        <div style="margin: 4px 0; color: #ffd700; font-size: 0.9rem;">
                            ${generateStarRating(attraction.rating)} ${attraction.rating.toFixed(1)}
                        </div>
                    ` : ''}
                    <p style="margin: 4px 0; color: #666; font-size: 0.8rem;">${attraction.distance.toFixed(1)} km away</p>
                    <button onclick="showAttractionDetails('${attraction.id}')"
                            style="background: #667eea; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 0.8rem; margin-top: 8px;">
                        View Details
                    </button>
                </div>
            `)
            .on('click', () => showAttractionDetails(attraction.id));

        markers.push(marker);
    });

    // Fit map to show all markers
    if (attractionsToShow.length > 0 && userLocation) {
        const group = new L.featureGroup([userMarker, ...markers]);
        map.fitBounds(group.getBounds().pad(0.1));
    }
}

/**
 * Show attraction details in modal
 */
function showAttractionDetails(attractionId) {
    const attraction = attractions.find(a => a.id === attractionId);
    if (!attraction) return;

    const modal = document.getElementById('attractionModal');
    const modalTitle = document.getElementById('modalTitle');
    const directionsBtn = document.getElementById('getDirectionsBtn');

    modalTitle.textContent = attraction.name;
    selectedPlace = attraction;

    // Display attraction details
    displayAttractionDetails(attraction);

    // Show directions button and set up click handler
    directionsBtn.style.display = 'block';
    directionsBtn.onclick = function() {
        const directionsOptions = document.getElementById('directionsOptions');
        directionsOptions.style.display = directionsOptions.style.display === 'none' ? 'flex' : 'none';
    };

    modal.style.display = 'block';

    // Highlight attraction card
    document.querySelectorAll('.attraction-card').forEach(card => {
        card.classList.remove('active');
    });
    document.querySelector(`[data-id="${attractionId}"]`)?.classList.add('active');
}

/**
 * Display attraction details in modal
 */
function displayAttractionDetails(attraction) {
    const modalContent = document.getElementById('modalContent');

    // Format opening hours
    let openingHoursHtml = '';
    if (attraction.opening_hours) {
        openingHoursHtml = `
            <div class="place-hours">
                <h5>Opening Hours</h5>
                <p>${attraction.opening_hours}</p>
            </div>
        `;
    }

    modalContent.innerHTML = `
        <div class="place-details">
            <div style="margin-bottom: 1rem;">
                <span class="attraction-category">${getCategoryLabel(attraction.category)}</span>
                <span class="attraction-distance" style="margin-left: 1rem;">${attraction.distance.toFixed(1)} km away</span>
            </div>

            ${attraction.rating > 0 ? `
                <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 1rem;">
                    <span style="color: #ffd700;">${generateStarRating(attraction.rating)}</span>
                    <span>${attraction.rating.toFixed(1)}/5.0</span>
                </div>
            ` : ''}

            ${attraction.address ? `<p><strong>Address:</strong> ${attraction.address}</p>` : ''}
            ${attraction.phone ? `<p><strong>Phone:</strong> <a href="tel:${attraction.phone}">${attraction.phone}</a></p>` : ''}
            ${attraction.website ? `<p><strong>Website:</strong> <a href="${attraction.website}" target="_blank">Visit Website</a></p>` : ''}
        </div>

        ${openingHoursHtml}
    `;
}

/**
 * Close modal and reset state
 */
function closeModal() {
    document.getElementById('attractionModal').style.display = 'none';
    document.getElementById('getDirectionsBtn').style.display = 'none';
    document.getElementById('directionsOptions').style.display = 'none';
    selectedPlace = null;

    document.querySelectorAll('.attraction-card').forEach(card => {
        card.classList.remove('active');
    });
}

/**
 * Get directions using Geoapify Routing API
 */
async function getDirections(travelMode = 'driving') {
    if (!userLocation || !selectedPlace) {
        showError('Unable to get directions. Please ensure location is available.');
        return;
    }

    // Hide directions options
    document.getElementById('directionsOptions').style.display = 'none';

    const destination = { lat: selectedPlace.lat, lng: selectedPlace.lng };

    try {
        // Convert travel mode to Geoapify format
        let mode;
        switch(travelMode) {
            case 'walking':
                mode = 'walk';
                break;
            case 'transit':
                mode = 'public_transport';
                break;
            case 'driving':
            default:
                mode = 'drive';
                break;
        }

        const params = {
            waypoints: `${userLocation.lat},${userLocation.lng}|${destination.lat},${destination.lng}`,
            mode: mode,
            format: 'json'
        };

        const data = await makeGeoapifyRequest(getConfig('GEOAPIFY_ROUTING_URL'), params);

        if (data.features && data.features.length > 0) {
            const route = data.features[0];
            const properties = route.properties;

            // Show route on map
            showRouteOnMap(route);

            // Show route information
            showRouteInfo({
                distance: { text: `${(properties.distance / 1000).toFixed(1)} km` },
                duration: { text: formatDuration(properties.time) },
                start_address: 'Your Location',
                end_address: selectedPlace.name
            }, travelMode);

            // Also open in external maps for mobile navigation
            const mapsUrl = `https://www.google.com/maps/dir/${userLocation.lat},${userLocation.lng}/${destination.lat},${destination.lng}`;

            // Ask user if they want to open in external maps app
            if (confirm(`Route found: ${(properties.distance / 1000).toFixed(1)} km, ${formatDuration(properties.time)}\n\nWould you like to open this route in Google Maps for turn-by-turn navigation?`)) {
                window.open(mapsUrl, '_blank');
            }
        } else {
            showError('No route found. Please try a different travel mode.');
        }

    } catch (error) {
        console.error('Directions request failed:', error);
        showError(getConfig('ERROR_MESSAGES').DIRECTIONS_API_ERROR);
    }
}

/**
 * Show route on map using Leaflet
 */
function showRouteOnMap(routeFeature) {
    // Remove existing route
    if (routeControl) {
        map.removeLayer(routeControl);
    }

    // Add route to map
    routeControl = L.geoJSON(routeFeature, {
        style: {
            color: '#667eea',
            weight: 5,
            opacity: 0.8
        }
    }).addTo(map);

    // Fit map to route
    map.fitBounds(routeControl.getBounds());
}

/**
 * Format duration from seconds to readable format
 */
function formatDuration(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (hours > 0) {
        return `${hours}h ${minutes}m`;
    } else {
        return `${minutes}m`;
    }
}

/**
 * Show route information
 */
function showRouteInfo(leg, travelMode) {
    const routeInfo = `
        <div style="background: #e3f2fd; border: 1px solid #bbdefb; border-radius: 6px; padding: 1rem; margin: 1rem 0;">
            <h5 style="margin-bottom: 0.5rem; color: #1565c0;">
                <i class="fas fa-route"></i> Route Information (${travelMode})
            </h5>
            <p style="margin: 0.25rem 0;"><strong>Distance:</strong> ${leg.distance.text}</p>
            <p style="margin: 0.25rem 0;"><strong>Duration:</strong> ${leg.duration.text}</p>
            <p style="margin: 0.25rem 0;"><strong>From:</strong> ${leg.start_address}</p>
            <p style="margin: 0.25rem 0;"><strong>To:</strong> ${leg.end_address}</p>
        </div>
    `;

    // Add route info to modal content
    const modalContent = document.getElementById('modalContent');
    const existingRouteInfo = modalContent.querySelector('.route-info');
    if (existingRouteInfo) {
        existingRouteInfo.remove();
    }

    const routeDiv = document.createElement('div');
    routeDiv.className = 'route-info';
    routeDiv.innerHTML = routeInfo;
    modalContent.appendChild(routeDiv);
}

/**
 * Show error message
 */
function showError(message) {
    const locationStatus = document.getElementById('locationStatus');
    locationStatus.className = 'location-status error';
    locationStatus.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
}

/**
 * Hide map loading overlay
 */
function hideMapLoading() {
    setTimeout(() => {
        const loadingElement = document.getElementById('mapLoading');
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
    }, 1000);
}
